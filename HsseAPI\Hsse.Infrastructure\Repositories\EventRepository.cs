using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class EventRepository : IEventRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public EventRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateOrUpdateEvent(CreateEventDto createEventDto)
        {
            if (createEventDto.EventID == 0)
            {
                // Create new event
                var newEvent = new MstEvents
                {
                    Title = createEventDto.Title,
                    Description = createEventDto.Description,
                    MediaURL = createEventDto.MediaURL,
                    EventDateTime = createEventDto.EventDateTime,
                    Location = createEventDto.Location,
                    ExternalLink = createEventDto.ExternalLink,
                    CreatedBy = createEventDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    FacilityID = createEventDto.FacilityID,
                    IsActive = createEventDto.IsActive,
                    ScheduleAt = createEventDto.ScheduleAt,
                    ExpiryAt = createEventDto.ExpiryAt,
                    IsRsvp = createEventDto.IsRsvp
                };

                _MasterDBContext.MstEvents.Add(newEvent);
                _MasterDBContext.SaveChanges();

                // Add receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        EventID = newEvent.EventID,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        EventID = newEvent.EventID,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return newEvent.EventID;
            }
            else
            {
                // Update existing event
                var existingEvent = _MasterDBContext.MstEvents
                    .FirstOrDefault(e => e.EventID == createEventDto.EventID);

                if (existingEvent == null)
                    return 0;

                existingEvent.Title = createEventDto.Title;
                existingEvent.Description = createEventDto.Description;
                existingEvent.MediaURL = createEventDto.MediaURL;
                existingEvent.EventDateTime = createEventDto.EventDateTime;
                existingEvent.Location = createEventDto.Location;
                existingEvent.ExternalLink = createEventDto.ExternalLink;
                existingEvent.FacilityID = createEventDto.FacilityID;
                existingEvent.IsActive = createEventDto.IsActive;
                existingEvent.ScheduleAt = createEventDto.ScheduleAt;
                existingEvent.ExpiryAt = createEventDto.ExpiryAt;
                existingEvent.IsRsvp = createEventDto.IsRsvp;

                // Remove existing receivers, then add new ones
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.EventID == createEventDto.EventID);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                // Add new receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        EventID = createEventDto.EventID,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        EventID = createEventDto.EventID,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return existingEvent.EventID;
            }
        }

        public int DeleteEvent(int eventId, int deletedBy)
        {
            var eventEntity = _MasterDBContext.MstEvents
                .FirstOrDefault(e => e.EventID == eventId);

            if (eventEntity == null)
                return 0; // Event not found

            // Set as inactive (soft delete)
            eventEntity.IsActive = false;

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public List<MstEvents> GetEvents()
        {
            return _MasterDBContext.MstEvents.ToList();
        }
    }
}
