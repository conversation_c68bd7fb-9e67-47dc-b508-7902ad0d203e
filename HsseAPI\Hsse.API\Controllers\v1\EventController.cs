using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class EventController : ControllerBase
    {
        private readonly IEventService _IEventService;
        private readonly ILogger<EventController> _logger;

        public EventController(IEventService eventService, IConfiguration configuration, ILogger<EventController> logger)
        {
            _logger = logger;
            _IEventService = eventService;
        }

        [HttpPost("CreateOrUpdateEvent")]
        public IActionResult CreateOrUpdateEvent([FromBody] CreateEventDto createEventDto)
        {
            long result = _IEventService.CreateOrUpdateEvent(createEventDto);
            return Ok(result);
        }

        [HttpDelete("DeleteEvent")]
        public IActionResult DeleteEvent(int eventId, int deletedBy)
        {
            int result = _IEventService.DeleteEvent(eventId, deletedBy);
            return Ok(result);
        }

        [HttpGet("GetEvents")]
        public IActionResult GetEvents()
        {
            var result = _IEventService.GetEvents();
            return Ok(result);
        }
    }
}
