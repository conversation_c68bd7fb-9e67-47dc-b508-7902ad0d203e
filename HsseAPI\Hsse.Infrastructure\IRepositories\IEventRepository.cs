using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IEventRepository
    {
        long CreateOrUpdateEvent(CreateEventDto createEventDto);
        int DeleteEvent(int eventId, int deletedBy);
        List<MstEvents> GetEvents();
    }
}
