﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class AnnouncementController : ControllerBase
    {
        private readonly IAnnouncementService _IAnnouncementService;
        private readonly ILogger<AnnouncementController> _logger;
        public AnnouncementController(IAnnouncementService announcementService, IConfiguration configuration, ILogger<AnnouncementController> logger)
        {
            _logger = logger;
            _IAnnouncementService = announcementService;
        }

        [HttpPost("CreateOrUpdateAnnouncement")]
        public IActionResult CreateOrUpdateAnnouncement([FromBody] CreateAnnouncementDto createAnnouncementDto)
        {
            long result = _IAnnouncementService.CreateOrUpdateAnnouncement(createAnnouncementDto);
            return Ok(result);
        }

        [HttpDelete("DeleteAnnouncement")]
        public IActionResult DeleteAnnouncement(int announcementId, int deletedBy)
        {
            int result = _IAnnouncementService.DeleteAnnouncement(announcementId, deletedBy);
            return Ok(result);
        }

        [HttpPost("CreateOrUpdateCategory")]
        public IActionResult CreateOrUpdateCategory([FromBody] CreateAnnouncementCategoryDto createCategoryDto)
        {
            int result = _IAnnouncementService.CreateOrUpdateCategory(createCategoryDto);
            return Ok(result);
        }

        [HttpGet("GetCategories")]
        public IActionResult GetCategories()
        {
            var result = _IAnnouncementService.GetCategories();
            return Ok(result);
        }

        [HttpGet("GetAnnouncements")]
        public IActionResult GetAnnouncements()
        {
            var result = _IAnnouncementService.GetAnnouncements();
            return Ok(result);
        }
    }
}
