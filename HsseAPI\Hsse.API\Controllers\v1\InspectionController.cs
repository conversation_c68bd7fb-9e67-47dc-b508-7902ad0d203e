﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionService _IInspectionService;
        private readonly ILogger<InspectionController> _logger;
        public InspectionController(IInspectionService inspectionService, IConfiguration configuration, ILogger<InspectionController> logger)
        {
            _logger = logger;
            _IInspectionService = inspectionService;
        }

        [HttpPost("CreateInspection")]
        public IActionResult CreateInspection([FromBody] CreateInspectionDto createInspectionDto)
        {
            long result = _IInspectionService.CreateInspection(createInspectionDto);
            return Ok(result);
        }

        [HttpPost("VerifyActionParty")]
        public IActionResult VerifyActionParty([FromBody] VerifyActionPartyDto verifyActionPartyDto)
        {
            int result = _IInspectionService.VerifyActionParty(verifyActionPartyDto);
            return Ok(result);
        }

        [HttpPost("VerifyInspector")]
        public IActionResult VerifyInspector([FromBody] VerifyInspectorDto verifyInspectorDto)
        {
            int result = _IInspectionService.VerifyInspector(verifyInspectorDto);
            return Ok(result);
        }

        [HttpGet("GetInspections")]
        public IActionResult GetInspections()
        {
            var result = _IInspectionService.GetInspections();
            return Ok(result);
        }

        [HttpGet("GetActionParties")]
        public IActionResult GetActionParties()
        {
            var result = _IInspectionService.GetActionParties();
            return Ok(result);
        }
    }
}
